/* AdminSidebar Component Styles */
.AdminSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  width: 100%;
  height: 100%;
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  position: relative;
}

.AdminSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Logo Section */
.AdminSidebar__logo {
  text-align: center;
  padding: var(--heading6) 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading6);
}

.AdminSidebar__logo h3 {
  color: var(--primary-color);
  font-size: var(--heading5);
  font-weight: 700;
  margin: 0;
}

.AdminSidebar__logo span {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Navigation Menu */
.AdminSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  gap: 4px;
}

.AdminSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
}

.AdminSidebar__item:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.AdminSidebar__item.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

.AdminSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
  min-width: 20px;
}

/* Logout Section */
.AdminSidebar__logout {
  margin-top: auto;
  border-top: 1px solid var(--light-gray);
  padding-top: var(--basefont);
}

.AdminSidebar__logout .logout-item {
  color: var(--btn-color);
  font-weight: 600;
}

.AdminSidebar__logout .logout-item:hover {
  background-color: rgba(238, 52, 37, 0.1);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminSidebar__item {
    padding: var(--smallfont) var(--basefont);
  }

  .AdminSidebar__item span {
    font-size: var(--smallfont);
  }
}

@media (max-width: 768px) {
  .AdminSidebar {
    /* Mobile sidebar is handled by AdminLayout positioning */
    background-color: var(--white);
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
  }

  .AdminSidebar__container {
    width: 100%;
    height: 100%;
    background-color: var(--white);
    overflow-y: auto;
    padding: var(--basefont);
    -webkit-overflow-scrolling: touch;
  }

  .AdminSidebar__logo {
    padding: var(--basefont) 0;
    border-bottom: 1px solid var(--bg-gray);
    margin-bottom: var(--basefont);
  }

  .AdminSidebar__logo h3 {
    font-size: var(--heading6);
    margin-bottom: 4px;
  }

  .AdminSidebar__logo span {
    font-size: var(--smallfont);
    color: var(--dark-gray);
  }

  .AdminSidebar__item {
    padding: var(--basefont);
    margin-bottom: 4px;
    border-radius: var(--border-radius);
  }

  .AdminSidebar__item:hover {
    background-color: var(--bg-gray);
  }

  .AdminSidebar__item.active {
    background-color: var(--primary-light-color);
    border-left: 4px solid var(--btn-color);
  }

  .AdminSidebar__item span {
    font-size: var(--smallfont);
    font-weight: 500;
  }

  .AdminSidebar__icon {
    font-size: var(--basefont);
    margin-right: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AdminSidebar__container {
    padding: var(--smallfont);
  }

  .AdminSidebar__logo {
    padding: var(--smallfont) 0;
    text-align: center;
  }

  .AdminSidebar__logo h3 {
    font-size: var(--heading6);
  }

  .AdminSidebar__item {
    padding: var(--smallfont) var(--basefont);
    margin-bottom: 2px;
  }

  .AdminSidebar__item span {
    font-size: var(--extrasmallfont);
  }

  .AdminSidebar__icon {
    font-size: var(--smallfont);
    margin-right: 8px;
  }
}

/* Collapsible sidebar for desktop */
.AdminSidebar.collapsed {
  width: 80px; /* Slightly wider for better icon visibility */
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  display: none;
}

.AdminSidebar.collapsed .AdminSidebar__logo h3 {
  font-size: var(--smallfont);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.AdminSidebar.collapsed .AdminSidebar__item {
  justify-content: center;
  padding: var(--basefont) var(--smallfont);
  position: relative;
}

.AdminSidebar.collapsed .AdminSidebar__icon {
  margin-right: 0;
}

/* Tooltip for collapsed sidebar */
.AdminSidebar.collapsed .AdminSidebar__item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--dark-gray);
  color: var(--white);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  white-space: nowrap;
  z-index: 1200;
  margin-left: 8px;
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Enhanced smooth transitions */
.AdminSidebar__logo span,
.AdminSidebar__item span {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  opacity: 0;
}

/* Scrollbar styling for fixed sidebar */
.AdminSidebar__container::-webkit-scrollbar {
  width: 6px;
}

.AdminSidebar__container::-webkit-scrollbar-track {
  background: var(--bg-gray);
  border-radius: 3px;
}

.AdminSidebar__container::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.AdminSidebar__container::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Active state enhancements */
.AdminSidebar__item.active .AdminSidebar__icon {
  transform: scale(1.1);
}

/* Hover effects */
.AdminSidebar__item:not(.active):hover .AdminSidebar__icon {
  transform: scale(1.05);
}

/* Focus states for accessibility */
.AdminSidebar__item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

/* Custom scrollbar for mobile */
@media (max-width: 768px) {
  .AdminSidebar__container::-webkit-scrollbar {
    width: 4px;
  }

  .AdminSidebar__container::-webkit-scrollbar-track {
    background: var(--bg-gray);
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb {
    background: var(--light-gray);
    border-radius: 2px;
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
  }
}
