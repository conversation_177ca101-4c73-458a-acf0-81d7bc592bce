/* AdminContentManagement Component Styles */
.AdminContentManagement {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminContentManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
}

.header-left {
  flex: 1;
  max-width: 400px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.search-input::placeholder {
  color: var(--dark-gray);
}

.header-right {
  display: flex;
  gap: var(--smallfont);
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

.btn.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.btn.btn-success:hover {
  background-color: #059669;
}

.btn.btn-warning {
  background-color: #f59e0b;
  color: var(--white);
}

.btn.btn-warning:hover {
  background-color: #d97706;
}

.btn.btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.btn.btn-danger:hover {
  background-color: #dc2626;
}

/* Filters */
.AdminContentManagement__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.filter-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.filter-select {
  padding: 6px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminContentManagement__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.content-table {
  width: 100%;
  border-collapse: collapse;
}

.content-table th,
.content-table td {
  padding: var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
}

.content-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.content-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.content-table tr:hover {
  background-color: var(--bg-gray);
}

/* Status Controls */
.status-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-published {
  background-color: #28a745;
  color: white;
}

.status-draft {
  background-color: #6c757d;
  color: white;
}

.status-sold {
  background-color: #17a2b8;
  color: white;
}

/* Toggle Button */
.btn-action.toggle {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.5rem;
  color: #28a745;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  background-color: transparent !important;
}

.btn-action.toggle:hover {
  color: #218838;
}

.btn-action.toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Content Info */
.content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-thumbnail {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.content-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-title {
  font-weight: 500;
  color: #212529;
}

.content-category {
  font-size: 12px;
  color: #6c757d;
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-action {
  background: none;
  border: none;
  padding: 5px;
  cursor: pointer;
  font-size: 1rem;
  color: #6c757d;
  transition: color 0.3s ease;
}

.btn-action.edit:hover {
  color: #28a745;
}

.btn-action.delete:hover {
  color: #dc3545;
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminContentManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminContentManagement__header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-left {
    max-width: none;
  }

  .AdminContentManagement__filters {
    flex-wrap: wrap;
  }

  .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
  }

  .content-table {
    font-size: var(--extrasmallfont);
  }

  .content-table th,
  .content-table td {
    padding: var(--smallfont);
  }

  .content-thumbnail {
    width: 40px;
    height: 30px;
    font-size: var(--smallfont);
  }

  .content-title {
    max-width: 150px;
  }

  .AdminContentManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .table-actions {
    flex-direction: column;
  }

  .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }
}
