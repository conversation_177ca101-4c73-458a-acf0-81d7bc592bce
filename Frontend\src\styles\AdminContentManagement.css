/* AdminContentManagement Component Styles */
.AdminContentManagement {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* Header Section */
.AdminContentManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
  color: white;
  position: relative;
  overflow: hidden;
}

.AdminContentManagement__header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-left {
  flex: 1;
  max-width: 500px;
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.header-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 20px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #64748b;
  font-size: 16px;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #64748b;
}

.header-right {
  display: flex;
  gap: 12px;
  position: relative;
  z-index: 1;
}

/* Modern Button Styles */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn.btn-outline {
  background-color: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn.btn-outline:hover {
  background-color: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.btn.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Modern Filters Section */
.AdminContentManagement__filters {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.filter-icon {
  color: #64748b;
  font-size: 18px;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  font-weight: 500;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-select:hover {
  border-color: #cbd5e1;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.selected-count {
  font-size: 14px;
  color: #475569;
  font-weight: 600;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* Modern Table Section */
.AdminContentManagement__table {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
  border-radius: 16px;
}

.content-table {
  width: 100%;
  border-collapse: collapse;
}

.content-table th,
.content-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.content-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 700;
  font-size: 12px;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.content-table td {
  font-size: 14px;
  color: #334155;
}

.content-table tr {
  transition: all 0.2s ease;
}

.content-table tr:hover {
  background-color: #f8fafc;
  transform: scale(1.001);
}

/* Modern Status Controls */
.status-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.status-published {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.status-draft {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}

.status-sold {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

/* Modern Toggle Button */
.btn-action.toggle {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  font-size: 20px;
  color: #10b981;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  border-radius: 8px;
  background-color: rgba(16, 185, 129, 0.1);
}

.btn-action.toggle:hover {
  color: #059669;
  background-color: rgba(16, 185, 129, 0.2);
  transform: scale(1.1);
}

.btn-action.toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Content Info */
.content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-thumbnail {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.content-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-title {
  font-weight: 500;
  color: #212529;
}

.content-category {
  font-size: 12px;
  color: #6c757d;
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.btn-action.view {
  background-color: var(--bg-blue);
  color: var(--btn-color);
}

.btn-action.view:hover {
  background-color: #e0f2fe;
  transform: scale(1.05);
}

.btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-action.edit:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.btn-action.delete:hover {
  background-color: #fee2e2;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminContentManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminContentManagement__header {
    gap: var(--basefont);
  }

  .header-left {
    max-width: 350px;
  }

  .content-table {
    font-size: var(--smallfont);
  }

  .content-table th,
  .content-table td {
    padding: var(--smallfont) 8px;
  }
}

@media (max-width: 768px) {
  .AdminContentManagement__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .header-left {
    max-width: none;
    min-width: auto;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .AdminContentManagement__filters {
    flex-wrap: wrap;
    gap: var(--smallfont);
    padding: var(--smallfont);
  }

  .filter-group {
    flex: 1;
    min-width: 120px;
  }

  .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
    justify-content: space-between;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .content-table {
    font-size: var(--extrasmallfont);
    min-width: 600px;
  }

  .content-table th,
  .content-table td {
    padding: var(--smallfont) 6px;
  }

  .content-thumbnail {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }

  .content-title {
    max-width: 120px;
    font-size: var(--extrasmallfont);
  }

  .table-actions {
    gap: 4px;
  }

  .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .AdminContentManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
    align-items: center;
  }

  .pagination-controls {
    order: -1;
  }
}

@media (max-width: 480px) {
  .AdminContentManagement {
    gap: var(--smallfont);
  }

  .AdminContentManagement__header {
    gap: 8px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .AdminContentManagement__filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 8px;
  }

  .content-table {
    min-width: 500px;
  }

  .table-actions {
    flex-direction: column;
    gap: 2px;
  }

  .btn-action {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  /* Hide tooltips on mobile */
  .btn-action::before {
    display: none;
  }

  .content-thumbnail {
    width: 24px;
    height: 24px;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .AdminContentManagement__table {
    margin: 0 -8px;
  }

  .table-container {
    border-radius: 0;
  }

  .content-table {
    min-width: 450px;
  }

  .content-info {
    gap: 6px;
  }

  .content-thumbnail {
    width: 20px;
    height: 20px;
  }
}
