/* AdminUserManagement Component Styles */
.AdminUserManagement {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminUserManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
  flex-wrap: wrap;
}

.header-left {
  flex: 1;
  max-width: 400px;
  min-width: 280px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.search-input::placeholder {
  color: var(--dark-gray);
}

.header-right {
  display: flex;
  gap: var(--smallfont);
  flex-wrap: wrap;
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

.btn.btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.btn.btn-danger:hover {
  background-color: #dc2626;
}

/* Filters */
.AdminUserManagement__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.filter-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.filter-select {
  padding: 6px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminUserManagement__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
}

.users-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.users-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.users-table tr:hover {
  background-color: var(--bg-gray);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--basefont);
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--secondary-color);
}

.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.role-badge.buyer {
  background-color: #dbeafe;
  color: #1e40af;
}

.role-badge.seller {
  background-color: #dcfce7;
  color: #166534;
}

.role-badge.admin {
  background-color: #fef3c7;
  color: #92400e;
}

.status-toggle {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fef2f2;
  color: #991b1b;
}

.status-badge.deleted {
  background-color: #f3f4f6;
  color: #6b7280;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--heading6);
  transition: all 0.3s ease;
}

.toggle-on {
  color: #10b981;
}

.toggle-off {
  color: var(--light-gray);
}

.toggle-btn:hover .toggle-on {
  color: #059669;
}

.toggle-btn:hover .toggle-off {
  color: var(--dark-gray);
}

.table-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.btn-action.view {
  background-color: var(--bg-blue);
  color: var(--btn-color);
}

.btn-action.view:hover {
  background-color: #e0f2fe;
  transform: scale(1.05);
}

.btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-action.edit:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.btn-action.delete:hover {
  background-color: #fee2e2;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminUserManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminUserManagement__header {
    gap: var(--basefont);
  }

  .header-left {
    max-width: 350px;
  }

  .users-table {
    font-size: var(--smallfont);
  }

  .users-table th,
  .users-table td {
    padding: var(--smallfont) 8px;
  }
}

@media (max-width: 768px) {
  .AdminUserManagement__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .header-left {
    max-width: none;
    min-width: auto;
  }

  .header-right {
    justify-content: flex-start;
  }

  .AdminUserManagement__filters {
    flex-wrap: wrap;
    gap: var(--smallfont);
    padding: var(--smallfont);
  }

  .filter-group {
    flex: 1;
    min-width: 120px;
  }

  .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
    justify-content: space-between;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .users-table {
    font-size: var(--extrasmallfont);
    min-width: 600px;
  }

  .users-table th,
  .users-table td {
    padding: var(--smallfont) 6px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--smallfont);
  }

  .user-details {
    gap: 2px;
  }

  .user-name {
    font-size: var(--extrasmallfont);
  }

  .table-actions {
    gap: 4px;
  }

  .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .AdminUserManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
    align-items: center;
  }

  .pagination-controls {
    order: -1;
  }
}

@media (max-width: 480px) {
  .AdminUserManagement {
    gap: var(--smallfont);
  }

  .AdminUserManagement__header {
    gap: 8px;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .AdminUserManagement__filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 8px;
  }

  .users-table {
    min-width: 500px;
  }

  .table-actions {
    flex-direction: column;
    gap: 2px;
  }

  .btn-action {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  /* Hide tooltips on mobile */
  .btn-action::before {
    display: none;
  }

  .status-toggle {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .toggle-btn {
    font-size: var(--smallfont);
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .AdminUserManagement__table {
    margin: 0 -8px;
  }

  .table-container {
    border-radius: 0;
  }

  .users-table {
    min-width: 450px;
  }

  .user-info {
    gap: 6px;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }
}
