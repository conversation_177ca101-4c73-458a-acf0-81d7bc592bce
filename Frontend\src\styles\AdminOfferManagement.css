.admin-offer-management {
  padding: 20px;
}

.admin-offer-management h2 {
  margin-bottom: 20px;
  color: #333;
}

/* Controls Section */
.controls-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.search-box,
.filter-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.search-box input {
  border: none;
  outline: none;
  padding: 5px;
  width: 250px;
}

.filter-box select {
  border: none;
  outline: none;
  padding: 5px;
  min-width: 150px;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.bulk-actions span {
  margin-right: 20px;
  color: #666;
}

.bulk-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.bulk-actions button:hover {
  opacity: 0.9;
}

.bulk-actions button.approve {
  background: #28a745;
  color: white;
}

.bulk-actions button.reject {
  background: #ffc107;
  color: #000;
}

.bulk-actions button.delete {
  background: #dc3545;
  color: white;
}

/* Table Styles */
.table-container {
  background: #fff;

  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

/* Content Cell */
.content-cell {
  display: flex;
  flex-direction: column;
}

.content-cell .title {
  font-weight: 500;
  color: #333;
}

.content-cell .details {
  font-size: 0.85em;
  color: #666;
}

/* User Cell */
.user-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-cell .profile-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-cell .user-info {
  display: flex;
  flex-direction: column;
}

.user-cell .name {
  font-weight: 500;
  color: #333;
}

.user-cell .email {
  font-size: 0.85em;
  color: #666;
}

/* Message Cell */
.message-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.accepted {
  background: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.expired {
  background: #e2e3e5;
  color: #383d41;
}

/* Action Buttons */
.actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: white;
}

.action-btn:hover {
  opacity: 0.9;
}

.action-btn.view {
  background: #17a2b8;
}

.action-btn.approve {
  background: #28a745;
}

.action-btn.reject {
  background: #ffc107;
  color: #000;
}

.action-btn.delete {
  background: #dc3545;
}

/* Loading and No Data States */
.loading,
.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding: 20px 0;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination button:not(:disabled):hover {
  background: #e9ecef;
}

.pagination span {
  color: #666;
}
