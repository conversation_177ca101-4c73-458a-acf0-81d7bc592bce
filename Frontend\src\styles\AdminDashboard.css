/* AdminDashboard Component Styles */
.AdminDashboard {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Metrics Cards */
.AdminDashboard__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border-left: 4px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.metric-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-card.clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-card.clickable:active {
  transform: translateY(-2px);
}

.metric-card.buyers {
  border-left-color: #3b82f6;
}

.metric-card.sellers {
  border-left-color: #10b981;
}

.metric-card.content {
  border-left-color: #f59e0b;
}

.metric-card.revenue {
  border-left-color: var(--btn-color);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: var(--heading4);
  color: var(--white);
}

.metric-card.buyers .metric-icon {
  background-color: #3b82f6;
}

.metric-card.sellers .metric-icon {
  background-color: #10b981;
}

.metric-card.content .metric-icon {
  background-color: #f59e0b;
}

.metric-card.revenue .metric-icon {
  background-color: var(--btn-color);
}

.metric-content {
  flex: 1;
}

.metric-number {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 4px;
}

.metric-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Section Styles */
.AdminDashboard__section {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.section-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.section-icon {
  color: var(--btn-color);
}

.approval-count {
  background-color: var(--btn-color);
  color: var(--white);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: var(--smallfont);
}

/* Approvals List */
.approvals-list {
  padding: var(--basefont);
  display: grid;
  gap: var(--basefont);
}

.approval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.approval-item:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.approval-content.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.approval-content h4 {
  margin: 0 0 4px 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.approval-content p {
  margin: 0 0 8px 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.approval-category {
  display: inline-block;
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: var(--smallfont);
}

.approval-date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.approval-actions {
  display: flex;
  gap: var(--smallfont);
}

.btn-approve,
.btn-reject,
.btn-view {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-approve {
  background-color: #10b981;
  color: var(--white);
}

.btn-approve:hover {
  background-color: #059669;
}

.btn-reject {
  background-color: #ef4444;
  color: var(--white);
}

.btn-reject:hover {
  background-color: #dc2626;
}

.btn-view {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-view:hover {
  background-color: var(--light-gray);
}

/* Tables Section */
.AdminDashboard__tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
}

.AdminDashboard__table-section {
  background-color: var(--white);
  border-radius: var(--border-radius);

  overflow: hidden;
}

.table-header {
  padding: var(--basefont) var(--heading6);
}

.table-header h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th,
.admin-table td {
  padding: var(--smallfont) var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
}

.admin-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.admin-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.admin-table tr:hover {
  background-color: var(--bg-gray);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--smallfont);
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fef2f2;
  color: #991b1b;
}

.status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.table-actions {
  display: flex;
  gap: 4px;
}

.btn-action {
  padding: 4px 8px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent !important;
}

.btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-action.edit:hover {
  background-color: var(--light-gray);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminDashboard__tables {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .AdminDashboard__metrics {
    grid-template-columns: 1fr 1fr;
  }

  .metric-card {
    padding: var(--basefont);
  }

  .metric-icon {
    width: 48px;
    height: 48px;
    font-size: var(--heading5);
  }

  .metric-number {
    font-size: var(--heading4);
  }

  .approval-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .approval-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .admin-table {
    font-size: var(--extrasmallfont);
  }

  .admin-table th,
  .admin-table td {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .AdminDashboard__metrics {
    grid-template-columns: 1fr;
  }

  .approval-actions {
    flex-wrap: wrap;
  }

  .btn-approve,
  .btn-reject,
  .btn-view {
    flex: 1;
    min-width: 80px;
    justify-content: center;
  }
}
