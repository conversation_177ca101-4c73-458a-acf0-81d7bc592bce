/* ContentDetailModal Component Styles */
.ContentDetailModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ContentDetailModal__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.ContentDetailModal__container {
  position: relative;
  background: white;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ContentDetailModal__header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #f8f9fa;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  flex: 1;
}

.content-thumbnail {
  /* width: 100px;
  height: 100px; */
  border-radius: 8px;
  overflow: hidden;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-thumbnail svg {
  /* width: 40px;
  height: 40px; */
  color: #666;
}

.content-basic-info {
  flex: 1;
}

.content-basic-info h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
}

.content-badges {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.status-badge,
.category-badge,
.type-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge {
  background: #e9ecef;
  color: #495057;
}

.status-badge.status-approved,
.status-badge.status-published {
  background: #d4edda;
  color: #155724;
  border-radius: 20px;
}

.status-badge.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.status-draft {
  background: #e9ecef;
  color: #495057;
}

.category-badge {
  background: #e7f5ff;
  color: #1864ab;
}

.type-badge {
  background: #f3f0ff;
  color: #5f3dc4;
}

.close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
}

.close-btn:hover {
  color: #333;
}

.ContentDetailModal__preview-section {
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.ContentDetailModal__video-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.ContentDetailModal__video {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
}

.ContentDetailModal__document-container {
  width: 100%;
  height: 500px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

/* Ensure proper spacing between sections */
.ContentDetailModal__content {
  padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ContentDetailModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .ContentDetailModal__preview-section {
    padding: 10px;
  }

  .ContentDetailModal__video,
  .ContentDetailModal__document-container {
    height: 300px;
  }
}

.info-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-icon {
  color: #666;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-icon {
  color: #666;
  font-size: 20px;
  margin-top: 3px;
}

.info-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

/* Status History Styles */
.status-history-list {
  margin-top: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.status-history-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  gap: 15px;
}

.status-history-item:last-child {
  border-bottom: none;
}

.status-details {
  flex: 1;
}

.status-timestamp {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.status-reason {
  font-size: 14px;
  color: #333;
}

.toggle-history-btn {
  margin-left: auto;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 12px;
}

.toggle-history-btn:hover {
  background: #f8f9fa;
}

/* Action Buttons */
.actions-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn svg {
  font-size: 16px;
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-outline {
  background: white;
  color: #666;
  border: 1px solid #ddd;
}

.btn-outline:hover {
  background: #f8f9fa;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
}

.btn-danger:hover {
  background: #c82333;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Status Colors */
.status-sold {
  color: #28a745;
}

.status-available {
  color: #17a2b8;
}

.status-active {
  color: #28a745;
}

.status-ended {
  color: #dc3545;
}

.status-upcoming {
  color: #ffc107;
}
